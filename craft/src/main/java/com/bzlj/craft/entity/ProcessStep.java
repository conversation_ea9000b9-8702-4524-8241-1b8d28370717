package com.bzlj.craft.entity;

import com.bzlj.base.generator.SnowflakeId;
import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.annotations.OnDelete;
import org.hibernate.annotations.OnDeleteAction;
import org.hibernate.type.SqlTypes;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;
import java.util.Map;

@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "process_step")
@Getter
@Setter
@EntityListeners(AuditingEntityListener.class)
public class ProcessStep {
    @Id
    @Size(max = 36)
    @Column(name = "id", nullable = false, length = 36)
    @SnowflakeId
    @GeneratedValue(generator = "snowflake")
    private String id;

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @OnDelete(action = OnDeleteAction.CASCADE)
    @JoinColumn(name = "process_id", nullable = false)
    @JsonIgnore
    private CraftProcess process;

    @Size(max = 50)
    @Column(name = "step_code", length = 10)
    private String stepCode;

    @Column(name = "step_name")
    private String stepName;

    @Column(name = "step_order", nullable = false)
    private Integer stepOrder;

    @Column(name = "param_config", nullable = false)
    @JdbcTypeCode(SqlTypes.JSON)
    private Map<String, Object> paramConfig;

    @Column(name = "quality_standard")
    @JdbcTypeCode(SqlTypes.JSON)
    private Map<String, Object> qualityStandard;

    @ManyToOne(fetch = FetchType.LAZY)
    @OnDelete(action = OnDeleteAction.SET_NULL)
    @JoinColumn(name = "base_step_id")
    private ProcessStep baseStep;

    @ColumnDefault("0")
    @Column(name = "modified")
    private Boolean modified;

    @Size(max = 50)
    @Column(name = "modified_by", length = 50)
    @LastModifiedBy
    private String modifiedBy;

    @Column(name = "modified_time")
    @LastModifiedDate
    private LocalDateTime modifiedTime;

}